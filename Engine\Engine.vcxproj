﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|Win32">
      <Configuration>Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{CE39975D-997D-48D5-B79B-98B3F3B36ED5}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <RootNamespace>Engine</RootNamespace>
    <WindowsTargetPlatformVersion>10.0</WindowsTargetPlatformVersion>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>NotSet</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <LinkIncremental>true</LinkIncremental>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <LinkIncremental>false</LinkIncremental>
    <OutDir>$(OutDir)</OutDir>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <ClCompile>
      <PrecompiledHeader>
      </PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Disabled</Optimization>
      <PreprocessorDefinitions>WIN32;_DEBUG;_WINDOWS;_USRDLL;ENGINE_EXPORTS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <ModuleDefinitionFile>Exports.def</ModuleDefinitionFile>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <PrecompiledHeader>
      </PrecompiledHeader>
      <Optimization>MaxSpeed</Optimization>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <PreprocessorDefinitions>WIN32;NDEBUG;_WINDOWS;_USRDLL;ENGINE_EXPORTS;_CRT_SECURE_NO_WARNINGS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
      <ModuleDefinitionFile>Exports.def</ModuleDefinitionFile>
      <GenerateMapFile>true</GenerateMapFile>
      <MapFileName>D:\Antrix 2019 - Copy\Engine.map</MapFileName>
      <MapExports>true</MapExports>
    </Link>
    <PostBuildEvent>
      <Command>
      </Command>
      <Message>
      </Message>
    </PostBuildEvent>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ClInclude Include="base64.h" />
    <ClInclude Include="Buff.h" />
    <ClInclude Include="Chatbox.h" />
    <ClInclude Include="dirent.h" />
    <ClInclude Include="DSSWindow.h" />
    <ClInclude Include="Engine.h" />
    <ClInclude Include="Exports.h" />
    <ClInclude Include="ExpTable.h" />
    <ClInclude Include="Graphics.h" />
    <ClInclude Include="Hardware.h" />
    <ClInclude Include="Hooks.h" />
    <ClInclude Include="Interface.h" />
    <ClInclude Include="Lock.h" />
    <ClInclude Include="MakeTip.h" />
    <ClInclude Include="MD5.h" />
    <ClInclude Include="MemoryPool.h" />
    <ClInclude Include="MemoryProtection.h" />
    <ClInclude Include="OnSend.h" />
    <ClInclude Include="Packets.h" />
    <ClInclude Include="Protect.h" />
    <ClInclude Include="RedBlue.h" />
    <ClInclude Include="Sha256.h" />
    <ClInclude Include="Tools.h" />
    <ClInclude Include="unzip.h" />
    <ClInclude Include="Variables.h" />
    <ClInclude Include="VButtonFix.h" />
    <ClInclude Include="zip.h" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Exports.def" />
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="base64.cpp" />
    <ClCompile Include="Buff.cpp" />
    <ClCompile Include="Chatbox.cpp" />
    <ClCompile Include="Engine.cpp" />
    <ClCompile Include="Graphics.cpp" />
    <ClCompile Include="Hooks.cpp" />
    <ClCompile Include="Interface.cpp" />
    <ClCompile Include="MemoryProtection.cpp" />
    <ClCompile Include="OnSend.cpp" />
    <ClCompile Include="Packets.cpp" />
    <ClCompile Include="Protect.cpp" />
    <ClCompile Include="Tools.cpp" />
    <ClCompile Include="unzip.cpp" />
    <ClCompile Include="Variables.cpp" />
    <ClCompile Include="zip.cpp" />
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>