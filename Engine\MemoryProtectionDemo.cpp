/*
 * Memory Protection Demonstration
 * This file demonstrates how the new memory protection system works
 * and can be used for testing purposes.
 */

#include "MemoryProtection.h"
#include <iostream>
#include <thread>
#include <chrono>

// Demo function to show memory protection in action
void DemonstrateMemoryProtection() {
    std::cout << "=== Project 404 Memory Protection Demo ===" << std::endl;
    
    // Initialize the memory protection system
    if (!MemoryProtection::Initialize()) {
        std::cout << "Failed to initialize memory protection!" << std::endl;
        return;
    }
    
    std::cout << "Memory protection system initialized successfully." << std::endl;
    std::cout << "Protection active: " << (MemoryProtection::IsProtectionActive() ? "Yes" : "No") << std::endl;
    
    // Add some custom blacklisted processes for testing
    MemoryProtection::AddBlacklistedProcess("notepad.exe");  // For testing purposes
    std::cout << "Added notepad.exe to blacklist for testing." << std::endl;
    
    // Demonstrate detection capabilities
    std::cout << "\n=== Running Detection Tests ===" << std::endl;
    
    // Test 1: Check for debuggers
    if (MemoryProtection::DetectDebuggers()) {
        std::cout << "WARNING: Debugger detected!" << std::endl;
    } else {
        std::cout << "No debuggers detected." << std::endl;
    }
    
    // Test 2: Check for memory editing tools
    if (MemoryProtection::DetectMemoryEditingTools()) {
        std::cout << "WARNING: Memory editing tools detected!" << std::endl;
    } else {
        std::cout << "No memory editing tools detected." << std::endl;
    }
    
    // Test 3: Check for virtual machines
    if (MemoryProtection::DetectVirtualMachines()) {
        std::cout << "INFO: Running in virtual machine." << std::endl;
    } else {
        std::cout << "Running on physical hardware." << std::endl;
    }
    
    // Test 4: Check for suspicious handles
    std::cout << "\nChecking for suspicious process handles..." << std::endl;
    if (MemoryProtection::DetectSuspiciousHandles()) {
        std::cout << "WARNING: Suspicious process handles detected!" << std::endl;
        
        auto suspiciousHandles = MemoryProtection::GetSuspiciousHandles();
        std::cout << "Found " << suspiciousHandles.size() << " suspicious processes:" << std::endl;
        
        for (const auto& handle : suspiciousHandles) {
            std::cout << "  - PID: " << handle.processId 
                      << ", Name: " << handle.processName 
                      << ", Path: " << handle.processPath << std::endl;
        }
    } else {
        std::cout << "No suspicious handles detected." << std::endl;
    }
    
    // Test 5: Protect a memory region
    std::cout << "\n=== Testing Memory Region Protection ===" << std::endl;
    
    // Allocate some test memory
    void* testMemory = VirtualAlloc(NULL, 4096, MEM_COMMIT | MEM_RESERVE, PAGE_READWRITE);
    if (testMemory) {
        // Write some test data
        strcpy_s((char*)testMemory, 4096, "This is protected test data");
        std::cout << "Allocated test memory at: 0x" << std::hex << testMemory << std::endl;
        
        // Protect the memory region
        if (MemoryProtection::ProtectMemoryRegion(testMemory, 4096, "TestRegion")) {
            std::cout << "Successfully protected memory region." << std::endl;
            
            // Try to read the protected memory (this should work)
            std::cout << "Reading protected memory: " << (char*)testMemory << std::endl;
            
            // Note: Writing to protected memory would trigger an access violation
            // which would be caught by our exception handler
            
        } else {
            std::cout << "Failed to protect memory region." << std::endl;
        }
        
        // Clean up
        MemoryProtection::UnprotectMemoryRegion(testMemory);
        VirtualFree(testMemory, 0, MEM_RELEASE);
        std::cout << "Cleaned up test memory." << std::endl;
    }
    
    // Generate a security report
    std::cout << "\n=== Security Report ===" << std::endl;
    MemoryProtection::GenerateSecurityReport();
    
    // Let the system run for a few seconds to demonstrate monitoring
    std::cout << "\nMonitoring system for 10 seconds..." << std::endl;
    std::this_thread::sleep_for(std::chrono::seconds(10));
    
    // Check if any security violations were detected
    if (g_bSecurityViolationDetected) {
        std::cout << "ALERT: Security violations were detected during monitoring!" << std::endl;
    } else {
        std::cout << "No security violations detected during monitoring." << std::endl;
    }
    
    // Shutdown the protection system
    MemoryProtection::Shutdown();
    std::cout << "\nMemory protection system shut down." << std::endl;
}

// Function to simulate a memory editing attempt (for testing)
void SimulateMemoryEditingAttempt() {
    std::cout << "\n=== Simulating Memory Editing Attempt ===" << std::endl;
    
    DWORD currentProcessId = GetCurrentProcessId();
    
    // Try to open our own process with suspicious access rights
    HANDLE hProcess = OpenProcess(
        PROCESS_VM_READ | PROCESS_VM_WRITE | PROCESS_VM_OPERATION,
        FALSE,
        currentProcessId
    );
    
    if (hProcess != NULL) {
        std::cout << "Successfully opened process with suspicious access rights." << std::endl;
        std::cout << "This would be detected by the memory protection system." << std::endl;
        CloseHandle(hProcess);
    } else {
        std::cout << "Failed to open process with suspicious access rights." << std::endl;
    }
}

// Function to test specific tool detection
void TestSpecificToolDetection() {
    std::cout << "\n=== Testing Specific Tool Detection ===" << std::endl;
    
    struct ToolTest {
        const char* name;
        bool (*detector)();
    };
    
    ToolTest tools[] = {
        {"Cheat Engine", DetectCheatEngine},
        {"Process Hacker", DetectProcessHacker},
        {"MemorySharp", DetectMemorySharp},
        {"x64dbg", Detectx64dbg},
        {"OllyDbg", DetectOllyDbg},
        {"IDA Pro", DetectIDA},
        {"WinAPI Override", DetectWinAPIOverride}
    };
    
    for (const auto& tool : tools) {
        if (tool.detector()) {
            std::cout << "DETECTED: " << tool.name << std::endl;
        } else {
            std::cout << "Not detected: " << tool.name << std::endl;
        }
    }
}

// Main demo function
void RunMemoryProtectionDemo() {
    std::cout << "Starting Project 404 Memory Protection Demo..." << std::endl;
    std::cout << "This demo shows how the memory protection system works." << std::endl;
    std::cout << "=================================================" << std::endl;
    
    try {
        DemonstrateMemoryProtection();
        SimulateMemoryEditingAttempt();
        TestSpecificToolDetection();
        
        std::cout << "\n=== Demo Complete ===" << std::endl;
        std::cout << "The memory protection system is now integrated into your engine." << std::endl;
        std::cout << "It will automatically start when the engine DLL is loaded." << std::endl;
        
    } catch (const std::exception& e) {
        std::cout << "Demo error: " << e.what() << std::endl;
    } catch (...) {
        std::cout << "Unknown error occurred during demo." << std::endl;
    }
}

/*
 * Usage Instructions:
 * 
 * 1. The memory protection system is automatically initialized when Engine.dll loads
 * 2. It runs in background threads monitoring for suspicious activity
 * 3. When a threat is detected, it will show a message and terminate the application
 * 4. You can customize the whitelist/blacklist by calling the appropriate functions
 * 5. The system protects against:
 *    - DLL injection (existing protection enhanced)
 *    - Memory editing tools (Cheat Engine, Process Hacker, etc.)
 *    - Debuggers and reverse engineering tools
 *    - Suspicious process handles accessing your game
 *    - Memory access violations
 * 
 * Configuration:
 * - Modify the whitelist/blacklist in MemoryProtection::Initialize()
 * - Adjust detection intervals by changing the constants in MemoryProtection.h
 * - Add custom memory regions to protect using ProtectMemoryRegion()
 */
