#include "MemoryProtection.h"
#include "Variables.h"
#include <iostream>
#include <sstream>
#include <algorithm>
#include <mutex>

// Include Detours for API hooking
#include "Detours/detours.h"

// Link required libraries
#pragma comment(lib, "psapi.lib")
#pragma comment(lib, "advapi32.lib")
#pragma comment(lib, "Detours/detours.lib")

// Global variables
volatile bool g_bMemoryProtectionEnabled = false;
volatile bool g_bSecurityViolationDetected = false;

// Static member initialization
std::atomic<bool> MemoryProtection::m_bProtectionActive(false);
std::atomic<bool> MemoryProtection::m_bShutdownRequested(false);
std::vector<ProcessHandleInfo> MemoryProtection::m_suspiciousHandles;
std::vector<ProtectedMemoryRegion> MemoryProtection::m_protectedRegions;
std::set<std::string> MemoryProtection::m_whitelistedProcesses;
std::set<std::string> MemoryProtection::m_blacklistedProcesses;
std::thread MemoryProtection::m_handleMonitorThread;
std::thread MemoryProtection::m_memoryIntegrityThread;

static std::mutex g_protectionMutex;

bool MemoryProtection::Initialize() {
    std::lock_guard<std::mutex> lock(g_protectionMutex);

    if (m_bProtectionActive.load()) {
        return true; // Already initialized
    }
    
    // Initialize whitelisted processes (system processes that legitimately access memory)
    m_whitelistedProcesses.insert("system");
    m_whitelistedProcesses.insert("csrss.exe");
    m_whitelistedProcesses.insert("winlogon.exe");
    m_whitelistedProcesses.insert("services.exe");
    m_whitelistedProcesses.insert("lsass.exe");
    m_whitelistedProcesses.insert("svchost.exe");
    m_whitelistedProcesses.insert("explorer.exe");
    m_whitelistedProcesses.insert("dwm.exe");
    m_whitelistedProcesses.insert("conhost.exe");

    // Additional legitimate processes that may access game memory
    m_whitelistedProcesses.insert("taskmgr.exe");          // Task Manager
    m_whitelistedProcesses.insert("perfmon.exe");          // Performance Monitor
    m_whitelistedProcesses.insert("procexp.exe");          // Process Explorer (if user has it)
    m_whitelistedProcesses.insert("procexp64.exe");        // Process Explorer 64-bit
    m_whitelistedProcesses.insert("tasklist.exe");         // Task List command
    m_whitelistedProcesses.insert("werfault.exe");         // Windows Error Reporting
    m_whitelistedProcesses.insert("wermgr.exe");           // Windows Error Reporting Manager
    m_whitelistedProcesses.insert("audiodg.exe");          // Audio Device Graph Isolation
    m_whitelistedProcesses.insert("fontdrvhost.exe");      // Font Driver Host
    m_whitelistedProcesses.insert("sihost.exe");           // Shell Infrastructure Host
    m_whitelistedProcesses.insert("ctfmon.exe");           // CTF Loader
    m_whitelistedProcesses.insert("searchindexer.exe");    // Windows Search Indexer
    m_whitelistedProcesses.insert("searchprotocolhost.exe"); // Search Protocol Host
    m_whitelistedProcesses.insert("runtimebroker.exe");    // Runtime Broker
    m_whitelistedProcesses.insert("backgroundtaskhost.exe"); // Background Task Host
    m_whitelistedProcesses.insert("applicationframehost.exe"); // Application Frame Host
    m_whitelistedProcesses.insert("winstore.app.exe");     // Windows Store App
    m_whitelistedProcesses.insert("smartscreen.exe");      // Windows Defender SmartScreen
    m_whitelistedProcesses.insert("antimalware service executable"); // Windows Defender
    m_whitelistedProcesses.insert("msmpeng.exe");          // Windows Defender Antimalware Service
    m_whitelistedProcesses.insert("nissrv.exe");           // Windows Defender Network Inspection Service
    
    // Initialize blacklisted processes (known memory editing tools)
    m_blacklistedProcesses.insert("cheatengine-x86_64.exe");
    m_blacklistedProcesses.insert("cheatengine-i386.exe");
    m_blacklistedProcesses.insert("cheatengine.exe");
    m_blacklistedProcesses.insert("processhacker.exe");
    m_blacklistedProcesses.insert("x64dbg.exe");
    m_blacklistedProcesses.insert("x32dbg.exe");
    m_blacklistedProcesses.insert("ollydbg.exe");
    m_blacklistedProcesses.insert("ida.exe");
    m_blacklistedProcesses.insert("ida64.exe");
    m_blacklistedProcesses.insert("windbg.exe");
    m_blacklistedProcesses.insert("memorysharp.exe");
    m_blacklistedProcesses.insert("artmoney.exe");
    m_blacklistedProcesses.insert("gameguardian.exe");
    m_blacklistedProcesses.insert("speedhack.exe");
    m_blacklistedProcesses.insert("tsearch.exe");
    
    // Set up exception handler for memory access violations
    SetUnhandledExceptionFilter(MemoryProtection::MemoryAccessViolationHandler);

#if ENABLE_API_HOOKS
    SetupMemoryAccessHooks();
#endif
    
    // Start monitoring threads
    m_bShutdownRequested.store(false);
    m_handleMonitorThread = std::thread(HandleMonitorThreadFunc);
    m_memoryIntegrityThread = std::thread(MemoryIntegrityThreadFunc);
    
    m_bProtectionActive.store(true);
    g_bMemoryProtectionEnabled = true;

    return true;
}

void MemoryProtection::Shutdown() {
    std::lock_guard<std::mutex> lock(g_protectionMutex);
    
    if (!m_bProtectionActive.load()) {
        return; // Already shut down
    }
    
    m_bShutdownRequested.store(true);

#if ENABLE_API_HOOKS
    // Remove memory access hooks
    RemoveMemoryAccessHooks();
#endif

    // Wait for threads to finish
    if (m_handleMonitorThread.joinable()) {
        m_handleMonitorThread.join();
    }
    if (m_memoryIntegrityThread.joinable()) {
        m_memoryIntegrityThread.join();
    }
    
    // Unprotect all memory regions
    for (auto& region : m_protectedRegions) {
        if (region.isActive) {
            UnprotectMemoryRegion(region.baseAddress);
        }
    }
    m_protectedRegions.clear();
    
    m_bProtectionActive.store(false);
    g_bMemoryProtectionEnabled = false;
}

bool MemoryProtection::DetectSuspiciousHandles() {
    HANDLE hSnapshot = CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0);
    if (hSnapshot == INVALID_HANDLE_VALUE) {
        return false;
    }

    PROCESSENTRY32 pe32;
    pe32.dwSize = sizeof(PROCESSENTRY32);

    bool suspiciousActivityDetected = false;
    DWORD currentProcessId = GetCurrentProcessId();

    // Optimized detection patterns for all tools in one pass
    static const std::vector<std::string> toolPatterns = {
        // Cheat Engine variants
        "cheatengine", "cheat engine", "cheatengine.exe", "cheatengine-x86_64.exe", "cheatengine-i386.exe",
        // Process Hacker
        "processhacker", "processhacker.exe",
        // MemorySharp and related
        "memorysharp", "memoryhacker", "memoryeditor", "memorywriter", "processeditor",
        // x64dbg/x32dbg
        "x64dbg.exe", "x32dbg.exe", "x64dbg", "x32dbg",
        // OllyDbg
        "ollydbg", "ollydbg.exe",
        // IDA Pro
        "ida.exe", "ida64.exe", "idaw.exe", "idaw64.exe",
        // Additional tools
        "winapioverride", "artmoney", "gameguardian", "speedhack", "tsearch", "windbg"
    };

    // Window class names for faster detection
    static const std::vector<std::pair<std::string, std::string>> windowChecks = {
        {"TMainForm", "cheat engine"},
        {"ProcessHacker", "process hacker"},
        {"Qt5QWindowIcon", "x64dbg"},
        {"OllyDbg", "ollydbg"}
    };

    // Check windows first (faster than process enumeration)
    for (const auto& windowCheck : windowChecks) {
        HWND hWnd = FindWindowA(windowCheck.first.c_str(), NULL);
        if (hWnd != NULL) {
            char windowTitle[256];
            if (GetWindowTextA(hWnd, windowTitle, sizeof(windowTitle))) {
                std::string title = windowTitle;
                std::transform(title.begin(), title.end(), title.begin(), ::tolower);
                if (title.find(windowCheck.second) != std::string::npos) {
                    CloseHandle(hSnapshot);
                    TriggerSecurityResponse("Memory editing tool window detected: " + windowCheck.second);
                    return true; // Early exit for immediate threat
                }
            }
        }
    }

    if (Process32First(hSnapshot, &pe32)) {
        int processCount = 0;
        const int MAX_PROCESSES_TO_CHECK = 200; // Limit to prevent excessive CPU usage

        do {
            if (++processCount > MAX_PROCESSES_TO_CHECK) {
                break; // Performance safeguard
            }

            if (pe32.th32ProcessID == currentProcessId) {
                continue; // Skip our own process
            }

            std::string processName = pe32.szExeFile;
            std::transform(processName.begin(), processName.end(), processName.begin(), ::tolower);

            // Fast pattern matching for all tools
            bool isKnownTool = false;
            std::string detectedTool;

            for (const auto& pattern : toolPatterns) {
                if (processName.find(pattern) != std::string::npos || processName == pattern) {
                    isKnownTool = true;
                    detectedTool = pattern;
                    break;
                }
            }

            if (isKnownTool || IsProcessBlacklisted(processName)) {
                CloseHandle(hSnapshot);
                TriggerSecurityResponse("Memory editing tool detected: " + (detectedTool.empty() ? processName : detectedTool));
                return true; // Immediate exit for known threats
            }

            // Only check suspicious patterns if not whitelisted (performance optimization)
            if (!IsProcessWhitelisted(processName) && IsSuspiciousProcessPattern(processName, pe32.th32ProcessID)) {
                ProcessHandleInfo handleInfo;
                handleInfo.processId = pe32.th32ProcessID;
                handleInfo.processName = processName;
                handleInfo.processPath = GetProcessPathFromPID(pe32.th32ProcessID);
                handleInfo.accessRights = SUSPICIOUS_ACCESS_RIGHTS;
                handleInfo.isWhitelisted = false;

                m_suspiciousHandles.push_back(handleInfo);
                suspiciousActivityDetected = true;
            }

        } while (Process32Next(hSnapshot, &pe32));
    }

    CloseHandle(hSnapshot);
    return suspiciousActivityDetected;
}



void MemoryProtection::HandleMonitorThreadFunc() {
    while (!m_bShutdownRequested.load()) {
        try {
#if ENABLE_HANDLE_MONITORING
            if (DetectSuspiciousHandles()) {
                g_bSecurityViolationDetected = true;
            }
#endif
            
            // Clean up old suspicious handle entries (older than 5 minutes)
            SYSTEMTIME currentTime;
            GetSystemTime(&currentTime);
            
            m_suspiciousHandles.erase(
                std::remove_if(m_suspiciousHandles.begin(), m_suspiciousHandles.end(),
                    [&currentTime](const ProcessHandleInfo& info) {
                        FILETIME currentFT, detectionFT;
                        SystemTimeToFileTime(&currentTime, &currentFT);
                        SystemTimeToFileTime(&info.detectionTime, &detectionFT);
                        
                        ULARGE_INTEGER current, detection;
                        current.LowPart = currentFT.dwLowDateTime;
                        current.HighPart = currentFT.dwHighDateTime;
                        detection.LowPart = detectionFT.dwLowDateTime;
                        detection.HighPart = detectionFT.dwHighDateTime;
                        
                        // Remove entries older than 5 minutes (300 seconds)
                        return (current.QuadPart - detection.QuadPart) > (300ULL * 10000000ULL);
                    }),
                m_suspiciousHandles.end()
            );
            
        } catch (...) {
            // Handle any exceptions to prevent thread termination
        }
        
        Sleep(HANDLE_CHECK_INTERVAL);
    }
}

void MemoryProtection::MemoryIntegrityThreadFunc() {
    while (!m_bShutdownRequested.load()) {
        try {
#if ENABLE_MEMORY_INTEGRITY
            if (!CheckMemoryIntegrity()) {
                TriggerSecurityResponse("Memory integrity violation detected");
                g_bSecurityViolationDetected = true;
            }
#endif

#if ENABLE_DEBUGGER_DETECTION
            if (DetectDebuggers()) {
                TriggerSecurityResponse("Debugger detected");
                g_bSecurityViolationDetected = true;
            }
#endif

        } catch (...) {
            // Handle any exceptions to prevent thread termination
        }

        Sleep(MEMORY_CHECK_INTERVAL);
    }
}

// Utility function implementations
bool MemoryProtection::IsProcessWhitelisted(const std::string& processName) {
    std::string lowerName = processName;
    std::transform(lowerName.begin(), lowerName.end(), lowerName.begin(), ::tolower);
    return m_whitelistedProcesses.find(lowerName) != m_whitelistedProcesses.end();
}

bool MemoryProtection::IsProcessBlacklisted(const std::string& processName) {
    std::string lowerName = processName;
    std::transform(lowerName.begin(), lowerName.end(), lowerName.begin(), ::tolower);
    return m_blacklistedProcesses.find(lowerName) != m_blacklistedProcesses.end();
}

std::string MemoryProtection::GetProcessNameFromPID(DWORD processId) {
    HANDLE hProcess = OpenProcess(PROCESS_QUERY_INFORMATION | PROCESS_VM_READ, FALSE, processId);
    if (hProcess == NULL) {
        return "Unknown";
    }

    char processName[MAX_PATH];
    if (GetModuleBaseNameA(hProcess, NULL, processName, sizeof(processName))) {
        CloseHandle(hProcess);
        return std::string(processName);
    }

    CloseHandle(hProcess);
    return "Unknown";
}

std::string MemoryProtection::GetProcessPathFromPID(DWORD processId) {
    // Simple cache to avoid repeated lookups for the same process
    static std::map<DWORD, std::string> pathCache;
    static DWORD lastClearTime = GetTickCount();

    // Clear cache every 30 seconds to prevent stale data
    DWORD currentTime = GetTickCount();
    if (currentTime - lastClearTime > 30000) {
        pathCache.clear();
        lastClearTime = currentTime;
    }

    auto it = pathCache.find(processId);
    if (it != pathCache.end()) {
        return it->second;
    }

    HANDLE hProcess = OpenProcess(PROCESS_QUERY_LIMITED_INFORMATION, FALSE, processId);
    if (hProcess == NULL) {
        pathCache[processId] = "Unknown";
        return "Unknown";
    }

    char processPath[MAX_PATH];
    if (GetModuleFileNameExA(hProcess, NULL, processPath, sizeof(processPath))) {
        CloseHandle(hProcess);
        std::string path = std::string(processPath);
        pathCache[processId] = path;
        return path;
    }

    CloseHandle(hProcess);
    pathCache[processId] = "Unknown";
    return "Unknown";
}



void MemoryProtection::TriggerSecurityResponse(const std::string& reason) {
    g_bSecurityViolationDetected = true;

    std::string message = "Security violation detected: " + reason +
                         "\n\nThe application will now terminate to protect Project 404 integrity.";
    MessageBoxA(NULL, message.c_str(), "Project 404 - Security Alert", MB_OK | MB_ICONERROR);

    extern bool StopClient;
    StopClient = true;
    exit(1);
}

bool MemoryProtection::CheckMemoryIntegrity() {
    // Check if any protected memory regions have been modified
    for (const auto& region : m_protectedRegions) {
        if (!region.isActive) continue;

        MEMORY_BASIC_INFORMATION mbi;
        if (VirtualQuery(region.baseAddress, &mbi, sizeof(mbi)) == 0) {
            return false; // Failed to query memory
        }

        // Check if protection has been changed
        if (mbi.Protect != region.currentProtection) {
            return false; // Memory protection has been modified
        }
    }

    return true;
}

bool MemoryProtection::ProtectMemoryRegion(LPVOID baseAddress, SIZE_T size, const std::string& regionName) {
    DWORD oldProtection;
    if (!VirtualProtect(baseAddress, size, PAGE_READONLY, &oldProtection)) {
        return false;
    }

    ProtectedMemoryRegion region;
    region.baseAddress = baseAddress;
    region.size = size;
    region.originalProtection = oldProtection;
    region.currentProtection = PAGE_READONLY;
    region.regionName = regionName;
    region.isActive = true;

    m_protectedRegions.push_back(region);
    return true;
}

bool MemoryProtection::UnprotectMemoryRegion(LPVOID baseAddress) {
    for (auto& region : m_protectedRegions) {
        if (region.baseAddress == baseAddress && region.isActive) {
            DWORD oldProtection;
            if (VirtualProtect(baseAddress, region.size, region.originalProtection, &oldProtection)) {
                region.isActive = false;
                return true;
            }
            return false;
        }
    }
    return false;
}

LONG WINAPI MemoryProtection::MemoryAccessViolationHandler(EXCEPTION_POINTERS* exceptionInfo) {
    if (exceptionInfo->ExceptionRecord->ExceptionCode == EXCEPTION_ACCESS_VIOLATION) {
        ULONG_PTR faultAddress = exceptionInfo->ExceptionRecord->ExceptionInformation[1];

        for (const auto& region : m_protectedRegions) {
            if (region.isActive &&
                faultAddress >= (ULONG_PTR)region.baseAddress &&
                faultAddress < (ULONG_PTR)region.baseAddress + region.size) {

                TriggerSecurityResponse("Unauthorized memory access detected");
                return EXCEPTION_EXECUTE_HANDLER;
            }
        }
    }

    return EXCEPTION_CONTINUE_SEARCH;
}
















bool IsRemoteDebuggerPresent_Advanced() {
    BOOL isRemoteDebuggerPresent = FALSE;

    // Use only the standard Windows API check to avoid false positives
    if (CheckRemoteDebuggerPresent(GetCurrentProcess(), &isRemoteDebuggerPresent)) {
        if (isRemoteDebuggerPresent) {
            return true;
        }
    }
    return false;
}




// Additional utility functions
std::string GetLastErrorString() {
    DWORD errorCode = GetLastError();
    LPSTR messageBuffer = nullptr;

    size_t size = FormatMessageA(
        FORMAT_MESSAGE_ALLOCATE_BUFFER | FORMAT_MESSAGE_FROM_SYSTEM | FORMAT_MESSAGE_IGNORE_INSERTS,
        NULL,
        errorCode,
        MAKELANGID(LANG_NEUTRAL, SUBLANG_DEFAULT),
        (LPSTR)&messageBuffer,
        0,
        NULL
    );

    std::string message(messageBuffer, size);
    LocalFree(messageBuffer);

    return message;
}

void SecureZeroMemoryEx(void* ptr, size_t size) {
    if (ptr != nullptr && size > 0) {
        SecureZeroMemory(ptr, size);
    }
}

bool IsRunningInVirtualMachine() {
    // Check for common VM artifacts

    // Check registry keys
    HKEY hKey;
    if (RegOpenKeyExA(HKEY_LOCAL_MACHINE, "SYSTEM\\CurrentControlSet\\Services\\VBoxService", 0, KEY_READ, &hKey) == ERROR_SUCCESS) {
        RegCloseKey(hKey);
        return true; // VirtualBox detected
    }

    if (RegOpenKeyExA(HKEY_LOCAL_MACHINE, "SOFTWARE\\VMware, Inc.\\VMware Tools", 0, KEY_READ, &hKey) == ERROR_SUCCESS) {
        RegCloseKey(hKey);
        return true; // VMware detected
    }

    // Check for VM-specific processes
    HANDLE hSnapshot = CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0);
    if (hSnapshot != INVALID_HANDLE_VALUE) {
        PROCESSENTRY32 pe32;
        pe32.dwSize = sizeof(PROCESSENTRY32);

        if (Process32First(hSnapshot, &pe32)) {
            do {
                std::string processName = pe32.szExeFile;
                std::transform(processName.begin(), processName.end(), processName.begin(), ::tolower);

                if (processName.find("vboxservice") != std::string::npos ||
                    processName.find("vmtoolsd") != std::string::npos ||
                    processName.find("vmwaretray") != std::string::npos ||
                    processName.find("vmwareuser") != std::string::npos) {
                    CloseHandle(hSnapshot);
                    return true;
                }
            } while (Process32Next(hSnapshot, &pe32));
        }
        CloseHandle(hSnapshot);
    }

    return false;
}

// Lightweight suspicious process pattern detection
bool IsSuspiciousProcessPattern(const std::string& processName, DWORD processId) {
    // Quick keyword check with minimal CPU impact
    static const std::vector<std::string> highRiskKeywords = {
        "hack", "cheat", "trainer", "inject", "memory", "edit", "patch", "crack"
    };

    int suspiciousScore = 0;

    // Fast string search for high-risk keywords
    for (const auto& keyword : highRiskKeywords) {
        if (processName.find(keyword) != std::string::npos) {
            suspiciousScore += 3; // Higher weight for definitive keywords
            break; // Exit early on first match for performance
        }
    }

    // Quick pattern checks
    if (processName.find(".tmp.exe") != std::string::npos ||
        processName.find("temp") != std::string::npos) {
        suspiciousScore += 2;
    }

    // Only do expensive path check if we already have some suspicion
    if (suspiciousScore >= 2) {
        HANDLE hProcess = OpenProcess(PROCESS_QUERY_LIMITED_INFORMATION, FALSE, processId);
        if (hProcess != NULL) {
            char processPath[MAX_PATH];
            if (GetModuleFileNameExA(hProcess, NULL, processPath, sizeof(processPath))) {
                std::string pathStr = processPath;
                std::transform(pathStr.begin(), pathStr.end(), pathStr.begin(), ::tolower);

                if (pathStr.find("\\temp\\") != std::string::npos ||
                    pathStr.find("\\tmp\\") != std::string::npos) {
                    suspiciousScore += 2;
                }
            }
            CloseHandle(hProcess);
        }
    }

    // Higher threshold to reduce false positives
    return suspiciousScore >= 4;
}

// Additional MemoryProtection member function implementations
void MemoryProtection::AddWhitelistedProcess(const std::string& processName) {
    std::lock_guard<std::mutex> lock(g_protectionMutex);
    std::string lowerName = processName;
    std::transform(lowerName.begin(), lowerName.end(), lowerName.begin(), ::tolower);
    m_whitelistedProcesses.insert(lowerName);
}

void MemoryProtection::AddBlacklistedProcess(const std::string& processName) {
    std::lock_guard<std::mutex> lock(g_protectionMutex);
    std::string lowerName = processName;
    std::transform(lowerName.begin(), lowerName.end(), lowerName.begin(), ::tolower);
    m_blacklistedProcesses.insert(lowerName);
}

void MemoryProtection::RemoveWhitelistedProcess(const std::string& processName) {
    std::lock_guard<std::mutex> lock(g_protectionMutex);
    std::string lowerName = processName;
    std::transform(lowerName.begin(), lowerName.end(), lowerName.begin(), ::tolower);
    m_whitelistedProcesses.erase(lowerName);
}

bool MemoryProtection::IsProtectionActive() {
    return m_bProtectionActive.load();
}

std::vector<ProcessHandleInfo> MemoryProtection::GetSuspiciousHandles() {
    std::lock_guard<std::mutex> lock(g_protectionMutex);
    return m_suspiciousHandles;
}

void MemoryProtection::ClearSuspiciousHandles() {
    std::lock_guard<std::mutex> lock(g_protectionMutex);
    m_suspiciousHandles.clear();
}

void MemoryProtection::GenerateSecurityReport() {
    // Report generation removed for performance
}

bool MemoryProtection::StartHandleMonitoring() {
    if (m_bProtectionActive.load()) {
        return true; // Already started
    }
    return Initialize();
}

void MemoryProtection::StopHandleMonitoring() {
    Shutdown();
}

bool MemoryProtection::DetectDebuggers() {
#if ENABLE_DEBUGGER_DETECTION
    // Only use the most reliable detection methods to avoid false positives
    // Advanced methods are disabled to prevent legitimate users from being blocked
    return IsDebuggerPresent_Advanced() || IsRemoteDebuggerPresent_Advanced();
#else
    // Debugger detection is disabled to prevent false positives
    return false;
#endif

    // Disabled aggressive checks that can cause false positives:
    // CheckForHardwareBreakpoints() || CheckForSoftwareBreakpoints()
}

bool MemoryProtection::DetectVirtualMachines() {
    return IsRunningInVirtualMachine();
}

bool MemoryProtection::SetupGuardPages() {
    // This would set up guard pages around critical memory regions
    // For now, we'll just return true as this is a complex implementation
    // that would require specific knowledge of the game's memory layout
    return true;
}

// Additional detection functions
bool DetectWinAPIOverride() {
    // Check for WinAPIOverride process
    HANDLE hSnapshot = CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0);
    if (hSnapshot != INVALID_HANDLE_VALUE) {
        PROCESSENTRY32 pe32;
        pe32.dwSize = sizeof(PROCESSENTRY32);

        if (Process32First(hSnapshot, &pe32)) {
            do {
                std::string processName = pe32.szExeFile;
                std::transform(processName.begin(), processName.end(), processName.begin(), ::tolower);

                if (processName.find("winapioverride") != std::string::npos) {
                    CloseHandle(hSnapshot);
                    return true;
                }
            } while (Process32Next(hSnapshot, &pe32));
        }
        CloseHandle(hSnapshot);
    }

    return false;
}

bool DetectHooks() {
    // Check for API hooks in critical functions
    // This is a simplified version - a full implementation would check
    // for inline hooks, IAT hooks, etc.

    HMODULE hKernel32 = GetModuleHandleA("kernel32.dll");
    if (hKernel32 == NULL) {
        return false;
    }

    // Check if OpenProcess has been hooked
    FARPROC pOpenProcess = GetProcAddress(hKernel32, "OpenProcess");
    if (pOpenProcess == NULL) {
        return false;
    }

    // Check first few bytes for common hook patterns
    BYTE* pBytes = (BYTE*)pOpenProcess;
    if (pBytes[0] == 0xE9 || pBytes[0] == 0xEB) { // JMP instruction
        return true; // Likely hooked
    }

    return false;
}



// API Hook implementations to block memory access
typedef HANDLE (WINAPI *pOpenProcess)(DWORD dwDesiredAccess, BOOL bInheritHandle, DWORD dwProcessId);
typedef BOOL (WINAPI *pReadProcessMemory)(HANDLE hProcess, LPCVOID lpBaseAddress, LPVOID lpBuffer, SIZE_T nSize, SIZE_T *lpNumberOfBytesRead);
typedef BOOL (WINAPI *pWriteProcessMemory)(HANDLE hProcess, LPVOID lpBaseAddress, LPCVOID lpBuffer, SIZE_T nSize, SIZE_T *lpNumberOfBytesWritten);
typedef BOOL (WINAPI *pVirtualProtectEx)(HANDLE hProcess, LPVOID lpAddress, SIZE_T dwSize, DWORD flNewProtect, PDWORD lpflOldProtect);

pOpenProcess OriginalOpenProcess = nullptr;
pReadProcessMemory OriginalReadProcessMemory = nullptr;
pWriteProcessMemory OriginalWriteProcessMemory = nullptr;
pVirtualProtectEx OriginalVirtualProtectEx = nullptr;

// Hooked OpenProcess function
HANDLE WINAPI HookedOpenProcess(DWORD dwDesiredAccess, BOOL bInheritHandle, DWORD dwProcessId) {
    DWORD currentProcessId = GetCurrentProcessId();

    // If someone is trying to open our process with suspicious access rights
    if (dwProcessId == currentProcessId) {
        // Only block if it's a combination of dangerous access rights
        DWORD dangerousAccess = PROCESS_VM_READ | PROCESS_VM_WRITE | PROCESS_VM_OPERATION;
        if ((dwDesiredAccess & dangerousAccess) == dangerousAccess) {
            // Get caller process info
            char callerPath[MAX_PATH];
            if (GetModuleFileNameA(NULL, callerPath, sizeof(callerPath))) {
                std::string callerName = callerPath;
                size_t lastSlash = callerName.find_last_of("\\/");
                if (lastSlash != std::string::npos) {
                    callerName = callerName.substr(lastSlash + 1);
                }
                std::transform(callerName.begin(), callerName.end(), callerName.begin(), ::tolower);

                // Allow whitelisted processes
                if (MemoryProtection::IsProcessWhitelisted(callerName)) {
                    return OriginalOpenProcess(dwDesiredAccess, bInheritHandle, dwProcessId);
                }

                // Only block if it's not our own process or a system process
                if (callerName != "engine.exe" && callerName != "engine.dll") {
                    // Log the attempt
                    std::string logMsg = "[SECURITY] Blocked suspicious OpenProcess from: " + callerName +
                                       " with access rights: 0x" + std::to_string(dwDesiredAccess) + "\n";
                    OutputDebugStringA(logMsg.c_str());

                    SetLastError(ERROR_ACCESS_DENIED);
                    return NULL;
                }
            }
        }
    }

    // Call original function
    return OriginalOpenProcess(dwDesiredAccess, bInheritHandle, dwProcessId);
}

// Hooked ReadProcessMemory function
BOOL WINAPI HookedReadProcessMemory(HANDLE hProcess, LPCVOID lpBaseAddress, LPVOID lpBuffer, SIZE_T nSize, SIZE_T *lpNumberOfBytesRead) {
#if ENABLE_READ_PROTECTION
    // Check if someone is trying to read our process memory
    DWORD processId = GetProcessId(hProcess);
    if (processId == GetCurrentProcessId()) {
        // Get caller process info
        char callerPath[MAX_PATH];
        if (GetModuleFileNameA(NULL, callerPath, sizeof(callerPath))) {
            std::string callerName = callerPath;
            size_t lastSlash = callerName.find_last_of("\\/");
            if (lastSlash != std::string::npos) {
                callerName = callerName.substr(lastSlash + 1);
            }
            std::transform(callerName.begin(), callerName.end(), callerName.begin(), ::tolower);

            // Allow whitelisted processes and our own process
            if (MemoryProtection::IsProcessWhitelisted(callerName) ||
                callerName == "engine.exe" || callerName == "engine.dll") {
                return OriginalReadProcessMemory(hProcess, lpBaseAddress, lpBuffer, nSize, lpNumberOfBytesRead);
            }

            if (nSize > 1024) {
                SetLastError(ERROR_ACCESS_DENIED);
                return FALSE;
            }
        }
    }
#endif

    return OriginalReadProcessMemory(hProcess, lpBaseAddress, lpBuffer, nSize, lpNumberOfBytesRead);
}

// Hooked WriteProcessMemory function
BOOL WINAPI HookedWriteProcessMemory(HANDLE hProcess, LPVOID lpBaseAddress, LPCVOID lpBuffer, SIZE_T nSize, SIZE_T *lpNumberOfBytesWritten) {
#if ENABLE_WRITE_PROTECTION
    // Check if someone is trying to write to our process memory
    DWORD processId = GetProcessId(hProcess);
    if (processId == GetCurrentProcessId()) {
        // Get caller process info
        char callerPath[MAX_PATH];
        if (GetModuleFileNameA(NULL, callerPath, sizeof(callerPath))) {
            std::string callerName = callerPath;
            size_t lastSlash = callerName.find_last_of("\\/");
            if (lastSlash != std::string::npos) {
                callerName = callerName.substr(lastSlash + 1);
            }
            std::transform(callerName.begin(), callerName.end(), callerName.begin(), ::tolower);

            // Allow whitelisted processes and our own process
            if (MemoryProtection::IsProcessWhitelisted(callerName) ||
                callerName == "engine.exe" || callerName == "engine.dll") {
                return OriginalWriteProcessMemory(hProcess, lpBaseAddress, lpBuffer, nSize, lpNumberOfBytesWritten);
            }

            MemoryProtection::TriggerSecurityResponse("External memory write attempt detected from: " + callerName);
            SetLastError(ERROR_ACCESS_DENIED);
            return FALSE;
        }
    }
#endif

    return OriginalWriteProcessMemory(hProcess, lpBaseAddress, lpBuffer, nSize, lpNumberOfBytesWritten);
}

// Hooked VirtualProtectEx function
BOOL WINAPI HookedVirtualProtectEx(HANDLE hProcess, LPVOID lpAddress, SIZE_T dwSize, DWORD flNewProtect, PDWORD lpflOldProtect) {
    // Check if someone is trying to change memory protection of our process
    DWORD processId = GetProcessId(hProcess);
    if (processId == GetCurrentProcessId()) {
        // Get caller process info
        char callerPath[MAX_PATH];
        if (GetModuleFileNameA(NULL, callerPath, sizeof(callerPath))) {
            std::string callerName = callerPath;
            size_t lastSlash = callerName.find_last_of("\\/");
            if (lastSlash != std::string::npos) {
                callerName = callerName.substr(lastSlash + 1);
            }
            std::transform(callerName.begin(), callerName.end(), callerName.begin(), ::tolower);

            // Allow whitelisted processes and our own process
            if (MemoryProtection::IsProcessWhitelisted(callerName) ||
                callerName == "engine.exe" || callerName == "engine.dll") {
                return OriginalVirtualProtectEx(hProcess, lpAddress, dwSize, flNewProtect, lpflOldProtect);
            }

            if (flNewProtect & (PAGE_EXECUTE | PAGE_EXECUTE_READ | PAGE_EXECUTE_READWRITE | PAGE_EXECUTE_WRITECOPY)) {
                SetLastError(ERROR_ACCESS_DENIED);
                return FALSE;
            }
        }
    }

    return OriginalVirtualProtectEx(hProcess, lpAddress, dwSize, flNewProtect, lpflOldProtect);
}

// Setup API hooks using Microsoft Detours (since you already have it)
bool SetupMemoryAccessHooks() {
    // Get original function addresses
    HMODULE hKernel32 = GetModuleHandleA("kernel32.dll");
    if (!hKernel32) {
        return false;
    }

    OriginalOpenProcess = (pOpenProcess)GetProcAddress(hKernel32, "OpenProcess");
    OriginalReadProcessMemory = (pReadProcessMemory)GetProcAddress(hKernel32, "ReadProcessMemory");
    OriginalWriteProcessMemory = (pWriteProcessMemory)GetProcAddress(hKernel32, "WriteProcessMemory");
    OriginalVirtualProtectEx = (pVirtualProtectEx)GetProcAddress(hKernel32, "VirtualProtectEx");

    if (!OriginalOpenProcess || !OriginalReadProcessMemory || !OriginalWriteProcessMemory || !OriginalVirtualProtectEx) {
        return false;
    }

    // Install hooks using Detours
    DetourTransactionBegin();
    DetourUpdateThread(GetCurrentThread());

    DetourAttach(&(PVOID&)OriginalOpenProcess, HookedOpenProcess);
    DetourAttach(&(PVOID&)OriginalReadProcessMemory, HookedReadProcessMemory);
    DetourAttach(&(PVOID&)OriginalWriteProcessMemory, HookedWriteProcessMemory);
    DetourAttach(&(PVOID&)OriginalVirtualProtectEx, HookedVirtualProtectEx);

    LONG result = DetourTransactionCommit();

    return (result == NO_ERROR);
}

void RemoveMemoryAccessHooks() {
    if (OriginalOpenProcess && OriginalReadProcessMemory && OriginalWriteProcessMemory && OriginalVirtualProtectEx) {
        DetourTransactionBegin();
        DetourUpdateThread(GetCurrentThread());

        DetourDetach(&(PVOID&)OriginalOpenProcess, HookedOpenProcess);
        DetourDetach(&(PVOID&)OriginalReadProcessMemory, HookedReadProcessMemory);
        DetourDetach(&(PVOID&)OriginalWriteProcessMemory, HookedWriteProcessMemory);
        DetourDetach(&(PVOID&)OriginalVirtualProtectEx, HookedVirtualProtectEx);

        DetourTransactionCommit();
    }
}
