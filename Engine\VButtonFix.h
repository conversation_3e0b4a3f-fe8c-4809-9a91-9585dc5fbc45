int __fastcall OnOk(void *Argument, void *edx)
{
	Interface<IPackets> Packets;

	if (*((DWORD *)Argument + 111) == 1000)
		Packets->Send(219, "dss", 1, Packets->RecCaller.c_str(), Packets->Recall.c_str());

	return Engine::KGameSys::OnOk(Argument);
}

int __fastcall OnCancel(void *Argument, void *edx)
{
	Interface<IPackets> Packets;

	if (*((DWORD *)Argument + 111) == 1000)
		Packets->Send(219,"dss",0,Packets->RecCaller.c_str(),Packets->Recall.c_str());

	return Engine::KGameSys::OnCancel(Argument);
}

int i = 0;
signed int __cdecl PressKey(int Word, void *edx)
{
	if ( Engine::KGameSys::PressedKey)
	{
		int ReCheck = Engine::KGameSys::Check();
		if ( ReCheck && ReCheck && (*(int (__thiscall **)(int))(*(DWORD *)ReCheck + 148))(ReCheck) )
		{
			return 0;
		}
		else {
			int MKey = PressableKey >> 19;
			if (MKey && (Word == MKey || Word == MKey - 32)) {
				if (*Engine::CGame_Character::m_Master) {
					PressableKey = 0;
					Interface<IPackets> Packets;
					for(int i=0;i<5;i++)
						Packets->Send(255, "dd", 6, numLock);
					return 1;
				}
			}

			if (Word == 118 || Word == 86)
			{
				if (*Engine::CGame_Character::m_Master) {
					VButton = 1;
					if (!Engine::KGameSys::WindowCheck("Honor"))
						Engine::KGameSys::OpenWindow("Honor", 0, 0, 0, 0, 0);
					else
						Engine::KGameSys::CloseWindow("Honor");
					VButton = 0;
				}
				return 1;
			}
			
			if (Word == 66 || Word == 98)
			{
				Interface<IPackets> Packets;
				Packets->Send(255, "d", 5);
				/*
				Packets->Send(4, "d", 190);
				Packets->Send(190, "");
				*/
			}
			
			if (Word == 79 || Word == 111)
			{
				if (*Engine::CGame_Character::m_Master) {
					Interface<IPackets> Packets;
					return Packets->Send(255, "d", 7);
				}
			}

			//if (Word == 69 || Word == 82)
			//{
			//	return 0;
			//}
		}
	}

	return Engine::KGameSys::PressKey(Word);
}

std::string IntToString(int value)
{
	// More efficient than stringstream for simple integer conversion
	return std::to_string(value);
}

int __fastcall OnUseItem(int m_pMacro, void* edx)
{
	if (!Engine::KGameSys::IsMoveable()) 
		return Engine::KGameSys::OnUseItem(m_pMacro);

	if (Engine::KGameSys::WindowCheck("bazaa") || Engine::KGameSys::WindowCheck("store2") || Engine::KGameSys::WindowCheck("storehonor") || Engine::KGameSys::WindowCheck("guildmsgbox")) 
		return Engine::KGameSys::OnUseItem(m_pMacro);
		
	//if chatting
	if (*(DWORD*)0x09CDC38 != 0 && *(DWORD*)0x09C961C) {
		int m_nClass = *(DWORD*)(m_pMacro + 92);
		switch (m_nClass)
		{
		case 11:
		case 12:
		case 13:
		case 14:
		case 15:
		case 16:
		case 19:
		case 20:
		case 22:
		case 25:
			return Engine::KGameSys::OnUseItem(m_pMacro);
		}
		int pItem = *(DWORD*)(m_pMacro + 88);
		int itemIndex = *(DWORD*)(pItem + 236) & 16777215;
		char type = *(DWORD*)(pItem + 236) >> 24;

		//buffs to chat
		//if (pItem && type == 5) {
		//		Engine::KGameSys::AddInfoMessage(Int2String(*(int*)pItem).c_str(), 54010, 0);
		//}

		//skills to chat
		if (pItem && type == 4) {
			unsigned long* myChat = (unsigned long*)((*Engine::KGameSys::m_pChattingControl) + 2712);
			int skillName = Engine::KGameSys::SkillName(pItem);

			if (Engine::KGameSys::SkillIsOnTimer(pItem)) {
				float myCD = Engine::KGameSys::SkillCooldown(pItem);
				std::string msg = " [" + (std::string)(const char*)skillName + "]> CD: "+ IntToString((int)(myCD * 100)) + "%";
				Engine::CEditor::Load(myChat, msg.c_str());
				(*(int(__thiscall**)(void*, signed int))(*myChat + 112))(myChat, 35);
			}
			else if (*(BYTE*)(pItem + 352) == 1) {
				double myPile = Engine::KGameSys::SkillPileCount(pItem);
				int myCharge = 0;
				if (myPile / 0.1000 == 2) {
					myCharge = 1;
				}
				else if (myPile / 0.1000 == 4) {
					myCharge = 2;
				}
				else if (myPile / 0.1000 == 6) {
					myCharge = 3;
				}
				else if (myPile / 0.1000 == 8) {
					myCharge = 4;
				}
				else if (myPile / 0.1000 == 10) {
					myCharge = 5;
				}

				std::string msg = " [" + (std::string)(const char*)skillName + "]> Charges: "+ IntToString(myCharge);
				Engine::CEditor::Load(myChat, msg.c_str());
				(*(int(__thiscall**)(void*, signed int))(*myChat + 112))(myChat, 35);
			}
			else {

				std::string msg = " [" + (std::string)(const char*)skillName + "]> Ready ";
				Engine::CEditor::Load(myChat, msg.c_str());
				(*(int(__thiscall**)(void*, signed int))(*myChat + 112))(myChat, 35);
			}
			return 0;
		}
		//item to chat
		//dic 34 = talisman
		//dic 44 = xMagic 43 = xAtk
		if (pItem && type == 2 && itemIndex) {
			int pInfo = Engine::KGameSys::FindItemFromIID(*(DWORD*)(m_pMacro + 116));
			unsigned long* myChat = (unsigned long*)((*Engine::KGameSys::m_pChattingControl) + 2712);
			int itemName = Engine::KGameSys::ItemName2(pItem, 1);
			std::string msg = "[" + (std::string)(const char*)itemName + "]";


			if (*(BYTE*)(pInfo + 43) > 0) {
				std::string myEnhAtk = IntToString(*(BYTE*)(pInfo + 43));
				msg += ">[+" + myEnhAtk + " ATK]";
			}else if (*(BYTE*)(pInfo + 44) > 0) {
				std::string myEnhAtk = IntToString(*(BYTE*)(pInfo + 44));
				msg += ">[+" + myEnhAtk + " MATK]";
			}

			if (*(BYTE*)(pInfo + 43) > 0 && *(BYTE*)(pInfo + 54) > 0) {
				std::string myEnhAtk = IntToString(*(BYTE*)(pInfo + 43));
				std::string myEnhEB = IntToString(*(BYTE*)(pInfo + 54));
				msg = "[" + (std::string)(const char*)itemName + "]>[" + myEnhAtk + "/" + myEnhEB + "]";
			}
			else if (*(BYTE*)(pInfo + 44) > 0 && *(BYTE*)(pInfo + 54) > 0) {
				std::string myEnhAtk = IntToString(*(BYTE*)(pInfo + 44));
				std::string myEnhEB = IntToString(*(BYTE*)(pInfo + 54));
				msg = "[" + (std::string)(const char*)itemName + "]>[" + myEnhAtk + "/" + myEnhEB + "]";
			}

			if (*(BYTE*)(pInfo + 46) > 0) {
				std::string myEnhOtp = IntToString(*(BYTE*)(pInfo + 46));
				msg += ">[+" + myEnhOtp + " OTP]";
			}

			if (*(BYTE*)(pInfo + 43) > 0 && *(BYTE*)(pInfo + 54) > 0 && *(BYTE*)(pInfo + 46) > 0) {
				std::string myEnhAtk = IntToString(*(BYTE*)(pInfo + 43));
				std::string myEnhEB = IntToString(*(BYTE*)(pInfo + 54));
				std::string myEnhOTP = IntToString(*(BYTE*)(pInfo + 46));
				msg = "[" + (std::string)(const char*)itemName + "]>[" + myEnhAtk + "/" + myEnhEB + "/" + myEnhOTP + "]";
			}
			else if (*(BYTE*)(pInfo + 44) > 0 && *(BYTE*)(pInfo + 54) > 0 && *(BYTE*)(pInfo + 46) > 0) {
				std::string myEnhAtk = IntToString(*(BYTE*)(pInfo + 44));
				std::string myEnhEB = IntToString(*(BYTE*)(pInfo + 54));
				std::string myEnhOTP = IntToString(*(BYTE*)(pInfo + 46));
				msg = "[" + (std::string)(const char*)itemName + "]>[" + myEnhAtk + "/" + myEnhEB + "/" + myEnhOTP + "]";
			}


			if (*(BYTE*)(pInfo + 34) > 0) {
				const char* myTail =  Engine::KGameSys::KMsgGet_(4, *(BYTE*)(pInfo + 34)) ;
				msg = "[("+(std::string)myTail+") " + msg.erase(0, 1);;
			}

			if (*(DWORD*)(pInfo + 36) > 1) {
				std::string myCount = IntToString(*(DWORD*)(pInfo + 36));
				msg = "[" + myCount + "x " + msg.erase(0, 1);;
			}


			msg += " ";


			Engine::CEditor::Load(myChat, msg.c_str());
			(*(int(__thiscall**)(void*, signed int))(*myChat + 112))(myChat, 35);
			//Engine::KGameSys::AddInfoMessage(IntToString(pInfo).c_str(), 54010, 0);
			return 0;
		}

	}
	
	
	
	return Engine::KGameSys::OnUseItem(m_pMacro);
}