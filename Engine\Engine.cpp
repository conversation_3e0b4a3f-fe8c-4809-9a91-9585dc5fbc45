#include <Windows.h>
#pragma pack(1)
HINSTANCE hL = 0;
FARPROC p[806] = { 0 };
#pragma comment(lib,"Detours/detours.lib")
#include "Detours/detours.h"
//#pragma comment(lib,"Detours/enigma_ide.lib")
//#include "Detours/enigma_ide.h"
#include "Engine.h"
#include "MemoryProtection.h"

#include "dirent.h"
#include <stdio.h>
#include <tchar.h>
#include <psapi.h>
#include "Variables.h"
#include "Exports.h"
#include "Protect.h"
#include "Interface.h"
#include "Chatbox.h"
#include "Packets.h"
#include "VButtonFix.h"
#include "Buff.h"
#include "Tools.h"
#include "DSSWindow.h"
#include "MakeTip.h"
#include "RedBlue.h"
#include "ExpTable.h"
#include "MD5.h"
#include "base64.h"
#include <process.h>
#include "Sha256.h"
#include <iterator>
#include <iomanip>
#include <sstream>
#include <wininet.h>
#include <fstream>
#include "unzip.h"
#include "MemoryPool.h"

#pragma comment(lib,"Wininet.lib")
#pragma comment(lib,"Ws2_32.lib")
#pragma comment( lib, "psapi.lib" )
#pragma warning (disable : 4996)
#pragma comment(lib,"version")
#define CRC32(c, b) ((*(pcrc_32_tab+(((int)(c) ^ (b)) & 0xff))) ^ ((c) >> 8))
int StoreValue = 0;

// Global memory pool instance
PacketMemoryPool g_PacketPool;

// Global storage for initial DLL list with enhanced information
std::set<DLLInfo> g_InitialDLLs;

void CaptureInitialDLLs() {
	// Clear any existing data
	g_InitialDLLs.clear();

	// Get current process handle
	HANDLE hProcess = GetCurrentProcess();
	HMODULE hMods[1024];
	DWORD cbNeeded;

	// Enumerate all modules in current process
	if (EnumProcessModules(hProcess, hMods, sizeof(hMods), &cbNeeded)) {
		// Calculate number of modules
		DWORD numModules = cbNeeded / sizeof(HMODULE);

		// Store each loaded module with enhanced information
		for (DWORD i = 0; i < numModules; i++) {
			char szModName[MAX_PATH];

			// Get module file name
			if (GetModuleFileNameExA(hProcess, hMods[i], szModName, sizeof(szModName))) {
				// Extract just the filename from full path
				char* fileName = strrchr(szModName, '\\');
				if (fileName) {
					fileName++; // Skip the backslash
				} else {
					fileName = szModName; // No path separator found
				}

				// Convert to lowercase for consistent comparison
				char lowerFileName[MAX_PATH];
				strcpy_s(lowerFileName, sizeof(lowerFileName), fileName);
				_strlwr_s(lowerFileName, sizeof(lowerFileName));

				// Get module information
				MODULEINFO modInfo;
				if (GetModuleInformation(hProcess, hMods[i], &modInfo, sizeof(modInfo))) {
					// Create DLL info structure
					DLLInfo dllInfo;
					dllInfo.name = std::string(lowerFileName);
					dllInfo.fullPath = std::string(szModName);
					dllInfo.handle = hMods[i];
					dllInfo.size = modInfo.SizeOfImage;

					// Store in our initial DLL set
					g_InitialDLLs.insert(dllInfo);
				}
			}
		}
	}
}


bool DetectDLLReplacement() {
	// Get current process handle
	HANDLE hProcess = GetCurrentProcess();
	HMODULE hMods[1024];
	DWORD cbNeeded;

	// Enumerate all modules in current process
	if (EnumProcessModules(hProcess, hMods, sizeof(hMods), &cbNeeded)) {
		// Calculate number of modules
		DWORD numModules = cbNeeded / sizeof(HMODULE);

		// Check each loaded module against our initial baseline
		for (DWORD i = 0; i < numModules; i++) {
			char szModName[MAX_PATH];

			// Get module file name
			if (GetModuleFileNameExA(hProcess, hMods[i], szModName, sizeof(szModName))) {
				// Extract just the filename from full path
				char* fileName = strrchr(szModName, '\\');
				if (fileName) {
					fileName++; // Skip the backslash
				} else {
					fileName = szModName; // No path separator found
				}

				// Convert to lowercase for comparison
				char lowerFileName[MAX_PATH];
				strcpy_s(lowerFileName, sizeof(lowerFileName), fileName);
				_strlwr_s(lowerFileName, sizeof(lowerFileName));

				// Get current module information
				MODULEINFO modInfo;
				if (GetModuleInformation(hProcess, hMods[i], &modInfo, sizeof(modInfo))) {
					// Check if this DLL was in our initial list
					bool foundInInitialList = false;
					for (const auto& initialDLL : g_InitialDLLs) {
						if (initialDLL.name == std::string(lowerFileName)) {
							foundInInitialList = true;
							// Found a DLL with the same name, verify it's the same DLL
							if (initialDLL.handle != hMods[i] || initialDLL.size != modInfo.SizeOfImage) {
								// Check if this is a legitimate system DLL that can change handles/sizes
								if (strcmp(lowerFileName, "comctl32.dll") == 0 ||
									strcmp(lowerFileName, "combase.dll") == 0 ||
									strcmp(lowerFileName, "shell32.dll") == 0 ||
									strcmp(lowerFileName, "ole32.dll") == 0 ||
									strcmp(lowerFileName, "oleaut32.dll") == 0 ||
									strcmp(lowerFileName, "user32.dll") == 0 ||
									strcmp(lowerFileName, "gdi32.dll") == 0) {
									// These system DLLs can legitimately change handles/sizes due to WinSxS
									break;
								}

								// Same name but different handle or size - DLL replacement detected!
								char debugMsg[512];
								sprintf_s(debugMsg, sizeof(debugMsg), "DLL Replacement Detected!\nDLL: %s\nPath: %s\nHandle changed: %s\nSize changed: %s",
									lowerFileName, szModName,
									(initialDLL.handle != hMods[i]) ? "Yes" : "No",
									(initialDLL.size != modInfo.SizeOfImage) ? "Yes" : "No");
								MessageBoxA(NULL, debugMsg, "Debug - DLL Replacement Detection", MB_OK | MB_ICONWARNING);
								return true;
							}
							break;
						}
					}

					// If DLL wasn't in initial list, check if it's a legitimate late-loading system DLL
					if (!foundInInitialList) {
						// Allow common system DLLs that load after startup
						if (strstr(lowerFileName, "api-ms-") ||
							strstr(lowerFileName, "ext-ms-") ||
							strstr(lowerFileName, "cryptbase.dll") ||
							strstr(lowerFileName, "bcryptprimitives.dll") ||
							strstr(lowerFileName, "sechost.dll") ||
							strstr(lowerFileName, "sspicli.dll") ||
							strstr(lowerFileName, "cryptsp.dll") ||
							strstr(lowerFileName, "rsaenh.dll") ||
							strstr(lowerFileName, "bcrypt.dll") ||
							strstr(lowerFileName, "ncrypt.dll") ||
							strstr(lowerFileName, "wintrust.dll") ||
							strstr(lowerFileName, "comctl32.dll") ||
							strstr(lowerFileName, "crypt32.dll") ||
							strstr(lowerFileName, "msasn1.dll") ||
							strstr(lowerFileName, "discordhook.dll") ||
							strstr(lowerFileName, "imagehlp.dll")) {
							// This is a legitimate system DLL, allow it
							continue;
						}

						// Check if it's from a system directory
						char lowerPath[MAX_PATH];
						strcpy_s(lowerPath, sizeof(lowerPath), szModName);
						_strlwr_s(lowerPath, sizeof(lowerPath));

						//								strstr(lowerPath, "\\program files\\") ||
								// strstr(lowerPath, "\\programdata\\") ||
								// strstr(lowerPath, "\\program files (x86)\\")
						if (strstr(lowerPath, "\\windows\\system32\\") ||
								strstr(lowerPath, "\\windows\\syswow64\\") ||
								strstr(lowerPath, "\\windows\\winsxs\\") ||
									strstr(lowerPath, "\\programdata\\microsoft\\")) {
							// System directory DLL, allow it
							continue;
						}

						// Unknown DLL not from system directory - potential injection
						char debugMsg[512];
						sprintf_s(debugMsg, sizeof(debugMsg), "DLL Injection Detected!\nDLL: %s\nPath: %s\nReason: Unknown DLL not from system directory",
							lowerFileName, szModName);
						MessageBoxA(NULL, debugMsg, "Debug - DLL Injection Detection", MB_OK | MB_ICONWARNING);
						return true;
					}
				}
			}
		}
	}

	// No DLL replacement detected
	return false;
}

bool DetectDLLInjection() {
	// Skip detection if initial DLLs haven't been captured yet
	if (g_InitialDLLs.empty()) {
		return false;
	}


	return DetectDLLReplacement();

	// Whitelist of legitimate system DLLs that can be loaded after startup
	const char* allowedLateLoadDLLs[] = {
		"comctl32.dll",
		"discordhook.dll"
	};

	const int numAllowedLateLoadDLLs = sizeof(allowedLateLoadDLLs) / sizeof(allowedLateLoadDLLs[0]);

	// Get current process handle
	HANDLE hProcess = GetCurrentProcess();
	HMODULE hMods[1024];
	DWORD cbNeeded;

	// Enumerate all modules in current process
	if (EnumProcessModules(hProcess, hMods, sizeof(hMods), &cbNeeded)) {
		// Calculate number of modules
		DWORD numModules = cbNeeded / sizeof(HMODULE);

		// Check each loaded module
		for (DWORD i = 0; i < numModules; i++) {
			char szModName[MAX_PATH];

			// Get module file name
			if (GetModuleFileNameExA(hProcess, hMods[i], szModName, sizeof(szModName))) {
				// Extract just the filename from full path
				char* fileName = strrchr(szModName, '\\');
				if (fileName) {
					fileName++; // Skip the backslash
				} else {
					fileName = szModName; // No path separator found
				}

				// Convert to lowercase for comparison
				char lowerFileName[MAX_PATH];
				strcpy_s(lowerFileName, sizeof(lowerFileName), fileName);
				_strlwr_s(lowerFileName, sizeof(lowerFileName));

				// Get current module information
				MODULEINFO modInfo;
				if (GetModuleInformation(hProcess, hMods[i], &modInfo, sizeof(modInfo))) {
					// Find the corresponding initial DLL info
					bool foundMatch = false;
					for (const auto& initialDLL : g_InitialDLLs) {
						if (initialDLL.name == std::string(lowerFileName)) {
							foundMatch = true;
							// Found a DLL with the same name, now verify it's the same DLL
							// Only check handle and size, skip path check for now as it might be causing issues
							if (initialDLL.handle != hMods[i] || initialDLL.size != modInfo.SizeOfImage) {
								// Check if this is a legitimate system DLL that can change handles/sizes
								if (strcmp(lowerFileName, "comctl32.dll") == 0 ||
									strstr(lowerFileName, "discordhook.dll") == 0) {
									// These system DLLs can legitimately change handles/sizes due to WinSxS
									break;
								}

								// Same name but different handle or size - DLL replacement detected!
								char debugMsg[512];
								sprintf_s(debugMsg, sizeof(debugMsg), "DLL Replacement Detected (DetectDLLInjection)!\nDLL: %s\nPath: %s\nHandle changed: %s\nSize changed: %s",
									lowerFileName, szModName,
									(initialDLL.handle != hMods[i]) ? "Yes" : "No",
									(initialDLL.size != modInfo.SizeOfImage) ? "Yes" : "No");
								MessageBoxA(NULL, debugMsg, "Project 404 - DEBUG-ONLY BEFORE RELEASE - DLL Injection Detection", MB_OK | MB_ICONWARNING);
								return true;
							}
							break;
						}
					}

					// If we didn't find a match, check if it's a legitimate system DLL
					if (!foundMatch) {
						bool isLegitimate = false;

						// Check if it's in the allowed late-load list
						for (int j = 0; j < numAllowedLateLoadDLLs; j++) {
							if (strcmp(lowerFileName, allowedLateLoadDLLs[j]) == 0) {
								isLegitimate = true;
								break;
							}
						}

						// Additional check: Allow DLLs from Windows system directories
						if (!isLegitimate) {
							char lowerPath[MAX_PATH];
							strcpy_s(lowerPath, sizeof(lowerPath), szModName);
							_strlwr_s(lowerPath, sizeof(lowerPath));

							// Allow DLLs from system directories
								// 							strstr(lowerPath, "\\program files\\") ||
								// strstr(lowerPath, "\\programdata\\") ||
								// strstr(lowerPath, "\\program files (x86)\\")

							if (strstr(lowerPath, "\\windows\\system32\\") ||
								strstr(lowerPath, "\\windows\\syswow64\\") ||
								strstr(lowerPath, "\\windows\\winsxs\\")) {
								isLegitimate = true;
							}
						}

						// Additional check: Allow Microsoft signed DLLs (basic check by common MS DLL patterns)
						if (!isLegitimate) {
							// Common Microsoft DLL prefixes/patterns
							if (strstr(lowerFileName, "api-ms-") ||
								strstr(lowerFileName, "ext-ms-") ||
								strstr(lowerFileName, "microsoft") ||
								strstr(lowerFileName, "msvc") ||
								strstr(lowerFileName, "ucrt") ||
								strstr(lowerFileName, "vcruntime") ||
								strstr(lowerFileName, "msvcp") ||
								strstr(lowerFileName, "concrt") ||
								strstr(lowerFileName, "vccorlib")) {
								isLegitimate = true;
							}
						}

						// If it's not legitimate, it's a real injection
						if (!isLegitimate) {
							char debugMsg[512];
							sprintf_s(debugMsg, sizeof(debugMsg), "DLL Injection Detected (DetectDLLInjection)!\nDLL: %s\nPath: %s\nReason: Not legitimate system DLL",
								lowerFileName, szModName);
							MessageBoxA(NULL, debugMsg, "Project 404 - DEBUG-ONLY BEFORE RELEASE - DLL Injection Detection", MB_OK | MB_ICONWARNING);
							return true;
						}
					}
				}
			}
		}
	}

	// No injection detected
	return false;
}

void init_keys(const unsigned long *pcrc_32_tab, const char *passwd, unsigned long *pkeys)
{
	*(pkeys + 0) = 305419896L;
	*(pkeys + 1) = 591751049L;
	*(pkeys + 2) = 878082192L;
	while (*passwd != '\0') {
		(*(pkeys + 0)) = CRC32((*(pkeys + 0)), (int)*passwd);
		(*(pkeys + 1)) += (*(pkeys + 0)) & 0xff;
		(*(pkeys + 1)) = (*(pkeys + 1)) * 134775813L + 1;
		{
			register int keyshift = (int)((*(pkeys + 1)) >> 24);
			(*(pkeys + 2)) = CRC32((*(pkeys + 2)), keyshift);
		}
		passwd++;
	}
}

int __cdecl PKHide(int a1, signed int a2, signed int a3, signed int a4, const char *a5)
{
	StoreValue = a1;
	return Engine::CEditor::PWDPK(a1, a2, a3, a4, a5);
}

void HideMyPKPWD()
{
		// config pwd
		char pwd[256] = "EV)O8@BL$3O2E";
		init_keys((unsigned long*)Engine::CEditor::AddPKPWD(), pwd, (unsigned long*)(StoreValue + 164));

}

void DisableProtection()
{
	Interface<IProtect> Protect;
	Protect->Disable();
	Interface<IChatbox> Chatbox;
	Chatbox->HookCommand();
	Interface<IPackets> Packets;
	Packets->Hook();
	Packets->DssUpdateCheck = false;
	Interface<IBuff> Buff;
	Buff->HookBuff();
	Interface<ITools> Tools;
	Tools->Intercept(ITools::_I_CALL, (void*)0x007E5024, HideMyPKPWD, 5);
}

std::string Int2Strings(int value)
{
	// More efficient than stringstream for simple integer conversion
	return std::to_string(value);
}

// Global flag to control thread termination
volatile bool g_bHackCheckRunning = true;
void DLLFolderInjectionChecks() {
	// List of allowed DLL files
	const char* allowedDLLs[] = {
		"BugTrap.dll",
		"d3dx9_29.dll",
		"dbghelp.dll",
		"Engine.dll",
		"MFC71.dll",
		"msvcp71.dll",
		"msvcr71.dll",
		"DotNetZip.dll"
	};
	const int numAllowedDLLs = sizeof(allowedDLLs) / sizeof(allowedDLLs[0]);

	// Open current directory
	DIR* dir = opendir(".");
	if (dir == NULL) {
		// If we can't open the directory, exit for security
		MessageBoxA(NULL, "Security violation detected. The application cannot verify file integrity and will now terminate.", "Project 404", MB_OK | MB_ICONERROR);
		StopClient = true;
		exit(1);
	}

	struct dirent* entry;
	while ((entry = readdir(dir)) != NULL) {
		// Check if the file has .dll extension (case insensitive)
		const char* filename = entry->d_name;
		size_t len = strlen(filename);

		if (len > 4) {
			const char* ext = filename + len - 4;
			if (_stricmp(ext, ".dll") == 0) {
				// Found a DLL file, check if it's in the allowed list
				bool isAllowed = false;
				for (int i = 0; i < numAllowedDLLs; i++) {
					if (_stricmp(filename, allowedDLLs[i]) == 0) {
						isAllowed = true;
						break;
					}
				}

				// If DLL is not in the allowed list, exit immediately
				if (!isAllowed) {
					closedir(dir);
					MessageBoxA(NULL, "Unauthorized file modification detected. The application has identified potentially malicious files and will now terminate to protect Project 404 integrity.", "Project 404", MB_OK | MB_ICONERROR);
					StopClient = true;
					exit(1);
				}
			}
		}
	}

	closedir(dir);
}
void HackCheck(void* Pack) {

	// Wait for the system to fully initialize before starting DLL checks
	Sleep(1000); // 1 second delay
	CaptureInitialDLLs();
	Sleep(2000); // 1 second delay
	while (g_bHackCheckRunning) {
		// Check for security violations from memory protection system
		//Sleep(10000); // 1 second delay
		if (g_bSecurityViolationDetected) {
			g_bHackCheckRunning = false;
			StopClient = true;
			MessageBoxA(NULL, "Security violation detected by memory protection system. The application will now terminate to protect Project 404 integrity.", "Project 404", MB_OK | MB_ICONERROR);
			exit(0);
		}

		// DLL Injection Detection Check
		if (DetectDLLInjection()) {
			g_bHackCheckRunning = false;
			StopClient = true;
			MessageBoxA(NULL, "Unauthorized file modification detected. The application has identified potentially malicious files and will now terminate to protect Project 404 integrity.", "Project 404", MB_OK | MB_ICONERROR);
			exit(0);
		}

		HWND consoleWnd = GetConsoleWindow();
		DWORD dwProcessId;
		GetWindowThreadProcessId(consoleWnd, &dwProcessId);
		if (consoleWnd && GetCurrentProcessId() == dwProcessId) {
			g_bHackCheckRunning = false;
			exit(1);
		}


		// Use shorter sleep intervals to allow for quicker thread termination
			Sleep(10000); // Total 10 seconds, but check termination flag every 100ms
	}
	_endthread();
}

// void FCheck(void* Pack) {
// 	std::vector<std::string> FileNames;
// 	char variable[256] = "";

// 	for (int i = 1; i <= 25; i++) {
// 		std::string key = "HiMbVoJZ" + Int2Strings(i);
// 		EP_ProtectedStringByKey(key.c_str(), variable, sizeof(variable));
// 		FileNames.push_back(variable);
// 		memset(variable, 0, sizeof(variable));
// 	}

// 	while (1) {
// 		for (auto x = FileNames.begin(); x != FileNames.end();x++) {
// 			std::ifstream infile(x->c_str());

// 			if (infile.good())
// 				exit(1);

// 			infile.close();
// 		}

// 		Sleep(20000);
// 	}
// }
//std::vector<std::string> Names;

void ChecksumCheck(void *Pack) {
	MD5Files.clear();
	//EP_ProtectedStringByKey("tlTGHTU8", Engine::KGameSys::zipCheck, sizeof(Engine::KGameSys::zipCheck));
	//Engine::KGameSys::zipCheck = "UXzq_s#^s-P#f87a-W4pQJyStVD8FPudU+Bzvbpn&amp;9kfZSZMK6&amp;8BQTf5PtE7H4#m^NtVLg?XdjKyQH_nLN$6d+dq=L7c7qJUYFA@WKQhgfRvhw2Jc$qV@q?aE5n859vt7URX=Q^cW!5wzdRzdS&amp;%A#AXDGYLcw=GTZ7BJM@P9ehQ2^s%pFC7#!b-tu=cW#s9nnNBfwANM4-C9WVYdvb@6NcxG2wMFBtdm!$eNXM*z?r%@TX485^yrVE7yaw?7rm"
	Sleep(2000);

	unsigned __int64 HashTotalCalculation = 0;
	MD5 Checks;

	// Pre-allocate vector to avoid reallocations
	std::vector<std::string> tempFiles;
	tempFiles.reserve(1000); // Reserve space for typical file count

	FILE *fileor = fopen("./checksum.db", "r");
	if (fileor != NULL)
	{
		char line[BUFSIZ];
		while (fgets(line, sizeof line, fileor) != NULL) {
			// Remove newline character if present to avoid unnecessary string operations
			size_t len = strlen(line);
			if (len > 0 && line[len-1] == '\n') {
				line[len-1] = '\0';
			}

			std::string xClientt(base64_decode(line));

			// Use faster file existence check
			DWORD fileAttrib = GetFileAttributesA(xClientt.c_str());
			if (fileAttrib == INVALID_FILE_ATTRIBUTES) {
				std::string Msg = "Missing file: " + xClientt + ".\nPlease update your client.";
				StopClient = true;
				MessageBoxA(0, Msg.c_str(), "Kalonline", MB_OK);
				exit(0);
			}

			HashTotalCalculation += sha256(Checks.digestFile((char*)xClientt.c_str()));
			HashTotalCalculation %= INT_MAX;

			tempFiles.push_back(std::move(xClientt)); // Use move to avoid copy
		}

		fclose(fileor);

		// Move the completed vector to MD5Files
		MD5Files = std::move(tempFiles);
	}

	Interface<IPackets> Packets;

	while (1) {
		if ((void*)*Engine::KSocket::g_lpClient) {
			if (!*Engine::CGame_Character::m_Master) {
				Sleep(1500);
				Packets->Send(214, "d", HashTotalCalculation);
			}

			if (!MD5Files.empty()) {
				int TB = GetTickCount();
				unsigned __int64 HashTotal = 0;
				const int Size = MD5Files.size(); // Make const since it doesn't change
				int countd = 0;

				// Pre-allocate string for performance
				std::string hashStr;
				hashStr.reserve(32); // Reserve space for typical hash string length

				for (int i = 1; i <= Size; i++) {
					HashTotal += sha256(Checks.digestFile((char*)MD5Files[i - 1].c_str())); // Keep cast for compatibility
					HashTotal %= INT_MAX;

					// Reduce sleep frequency - only sleep every 10 files instead of every file
					if (i % 10 == 0) {
						Sleep(1); // Reduced sleep time
					}

					if ((i % 500) == 0 || i == Size) {
						int TA = GetTickCount() - TB;
						int Ch = ((TB - TA) + (int)HashTotal + countd);

						// More efficient string conversion
						hashStr.clear();
						hashStr = std::to_string(Ch);

						InterlockedExchange(&MD5Time, TB);
						InterlockedExchange(&DelayCheck, TA);
						InterlockedExchange(&CurHash, sha256(hashStr) % INT_MAX);
						InterlockedExchange(&CurPos, countd);
						InterlockedExchange(&MD5TimeToSleep, 1);

						while (InterlockedExchangeAdd(&MD5TimeToSleep, 0))
							Sleep(50);

						TB = GetTickCount();
						countd++;
					}
				}
			}
			else
				break;
		}
		else
			Sleep(500);
	}
	_endthread();
}

int __fastcall Disconnect(void *a, void *edx) {
	UniqueKey = 0;
	CryptKey = 0;
	return Engine::KSocket::Disconnect(a);
}

void* __fastcall Destroy(void *a, void *edx) {
	UniqueKey = 0;
	CryptKey = 0;
	return Engine::KSocket::Destroy(a);
}

signed int __fastcall XTrapDisable(void *a, void *edx, int b) {
	*Engine::XTrap::hModule = (HMODULE)1;
	return 1;
}

int __fastcall SendPacket(void *Socket, void* edx, const char* Buffer, int Length)
{
	if(*(unsigned char*)(Buffer + 2) == latestPacket)
		return Engine::KSocket::SendPacket(Socket, Buffer, Length);

	return 0;
}
int HonorType = 0;

char* __fastcall UpdateHonorName(void* Player, void* edx, int Type)
{
	LockHonor();
	auto it = PlayerHonors.find((int)Player);
	if (it != PlayerHonors.end()) {
		HonorStruct honor = it->second;
		HonorType = honor.Value;
		*(DWORD *)((int)Player + 19572) = honor.Range;
	}
	UnlockHonor();

	char* Check = Engine::KGameSys::UpdateHonorType(Player, Type);

	HonorType = 0;
	return Check;
}

int count = 0;
char* __cdecl HonorName(int a1)
{
	if (a1 >= 1389 && a1 <= 1399) {
		if (VButton && MyHonor) {
			count++;
			if (count == 1)
				a1 = MyHonor;
			else {
				if (a1 != 1389)
					a1 = MyHonor - 1;
				else a1 = MyHonor;
				count = 0;
			}
		}
		else
		if (HonorType)
			a1 = HonorType;
	}

	return Engine::KGameSys::HonorName(a1);
}

void __fastcall LoadLogo(int window, void *edx) {
	/*if (!hL2) {
		abort();
		exit(1);
	}*/

	return Engine::KGameSys::LoadLogo(window);
}

//void __cdecl ChatBoxText(char Type, const char* Message, int Color)
//{
	//if (Message && strlen(Message) && strstr(Message, "[Bot]:"))
		//exit(1);

//	return Engine::KGameSys::AddChattingMessage(Type, Message, Color);
//}

int __fastcall PressKeyState(void *_this, void *edx, HWND hWnd, UINT Msg, WPARAM wParam, LPARAM lParam) {
	if (wParam >= 48 && wParam <= 57 && numPadClick != wParam) {
		if (numPadClick == -1)
			numPadClick = 0;
		else
			numPadClick = wParam;
	}

	int Check = Engine::KGameSys::PressKeyState(_this, hWnd, Msg, wParam, lParam);

	if (wParam >= 48 && wParam <= 57)
		numPadClick = 0;

	return Check;
}

void* __stdcall PacketParser(void* Packet, int Format) {

	Console();
	printf("parse %s\n", *(const char**)Format);

	return Engine::KSocket::PacketParser(Packet, Format);
}

void* __stdcall PacketParser2(void* Packet, int Format) {

	Console();
	printf("parse2 %s\n", *(const char**)Format);

	return Engine::KSocket::PacketParser2(Packet, Format);
}

char* __cdecl PacketParser3(int a1, void* Packet, int Format) {

	Console();
	printf("parse3 %s\n", (const char*)a1);

	return Engine::KSocket::PacketParser3(a1, Packet, Format);
}

int __cdecl CanAttack() {
	if (*Engine::CGame_Character::m_Master) {
		unsigned long CheckP = Engine::CGame_Character::FindCharacter(*Engine::Target, 0);

		if (CheckP) {
			if ((*(DWORD *)(*Engine::CGame_Character::m_Master + 18284) & 0x20000) && (*(DWORD *)(CheckP + 18284) & 0x40000))
				return 1;

			if ((*(DWORD *)(*Engine::CGame_Character::m_Master + 18284) & 0x40000) && (*(DWORD *)(CheckP + 18284) & 0x20000))
				return 1;
		}
	}

	return Engine::KGameSys::CanAttack();
}

int __cdecl WindowClose(int Window) {
	if (Window) {
		// More efficient: avoid string creation, use direct comparison
		const char* WindowName;
		if (*(DWORD *)(Window + 400) < 0x10u)
			WindowName = (const char *)(Window + 380);
		else
			WindowName = *(const char **)(Window + 380);

		// Use fast string comparison with early exit
		if (WindowName && WindowName[0] == 's' && strcmp(WindowName, "spd_PersonList") == 0)
			*Engine::Click = 0;
	}

	return Engine::KGameSys::DismissWindow(Window);
}

bool replace(std::string& str, const std::string& from, const std::string& to) {
	size_t start_pos = str.find(from);
	if (start_pos == std::string::npos)
		return false;
	str.replace(start_pos, from.length(), to);
	return true;
}

// void __fastcall ZIPProtection(int Window, void* edx)
// {
// 	int MapX = *(DWORD *)(Window + 140);
// 	int MapY = *(DWORD *)(Window + 144);

// 	std::string Folder = "Data\\MAPS\\";

// 	char x[BUFSIZ];
// 	sprintf(x, "%sn_%03d_%03d.xea", Folder.c_str(), MapX, MapY);
// 	std::ifstream infile(x);

// 	if (infile.good()) {
// 		infile.close();

// 		HZIP hz = OpenZip(x, Engine::KGameSys::zipCheck);

// 		if (hz) {
// 			ZIPENTRY ze;
// 			GetZipItem(hz, -1, &ze); int numitems = ze.index;

// 			for (int i = 0; i < numitems; i++) {
// 				GetZipItem(hz, i, &ze);
// 				std::string FName = Folder + ze.name;
// 				Names.push_back(FName);
// 				UnzipItem(hz, i, FName.c_str());
// 			}
// 			CloseZip(hz);
// 		}
// 	}

// 	Engine::KGameSys::MapReadFile(Window);

// 	std::vector< std::string >::iterator it = Names.begin();
// 	while (it != Names.end()) {
// 		if (!remove(it->c_str()))
// 			it = Names.erase(it);
// 		else
// 			++it;
// 	}
// }

int String2Ints(const std::string& String)
{
	try {
		return std::stoi(String);
	}
	catch (const std::exception&) {
		return 0; // Return 0 on conversion error
	}
}

int GetWinBuild() {
	static int cachedBuild = -1;

	// Cache the result since Windows build doesn't change during runtime
	if (cachedBuild != -1) {
		return cachedBuild;
	}

	HKEY hKey = 0;
	int Build = 0;
	wchar_t buf[255];
	DWORD dwBufSize = sizeof(buf);

	if (RegOpenKeyExW(HKEY_LOCAL_MACHINE, L"SOFTWARE\\Microsoft\\Windows NT\\CurrentVersion", 0, KEY_READ | KEY_WOW64_64KEY, &hKey) == ERROR_SUCCESS)
	{
		if (RegQueryValueExW(hKey, L"ReleaseId", 0, 0, (BYTE*)buf, &dwBufSize) == ERROR_SUCCESS) {
			std::wstring ws(buf);
			std::string str(ws.begin(), ws.end());
			Build = String2Ints(str);
		}
		RegCloseKey(hKey);
	}

	cachedBuild = Build;
	return Build;
}

int __cdecl CSkillExecute(unsigned __int8 a1, unsigned __int8 a2) {
	//stun enable
	if (!(*(DWORD*)(*Engine::CGame_Character::m_Master + 20672) == 2 && a1 == 24) && *(DWORD *)(*Engine::CGame_Character::m_Master + 18328) & 0x100) {
		(*(void(__thiscall **)(int, DWORD, DWORD, DWORD))(*(DWORD *)*Engine::CGame_Character::m_Master + 140))(
			*Engine::CGame_Character::m_Master,
			0,
			0,
			0);
		Engine::CSkill::Reset(0);
		Engine::CSkill::Clear();
		return 0;
	}

	return Engine::CSkill::Execute(a1, a2);
}

int __cdecl XTrapLaunch(int a1, int a2, BYTE* a3) {
	return 0;
}

int __fastcall CreateItem(int Item, void* edx, int Packet)
{
	//Timer fix
	if (*(DWORD *)(Packet + 27))
		*(DWORD *)(Item + 72) = GetTickCount();

	return Engine::KGameSys::CreateItem(Item, Packet);
}

void* __fastcall CreateRidingInventorySlots(void* _this, void* edx)
{
	// Detoured function - return 0 to disable the original functionality
	return 0;
}

BOOL APIENTRY DllMain(HMODULE hModule, DWORD ul_reason_for_call, LPVOID lpReserved)
{
	switch (ul_reason_for_call)
	{
	case DLL_PROCESS_ATTACH:
	{

		

		InitializeADVAPI32();
		BofFix();
		SwitchTable();
		DLLFolderInjectionChecks();

		// Initialize advanced memory protection
		if (!MemoryProtection::Initialize()) {
			MessageBoxA(NULL, "Failed to initialize memory protection system.", "Project 404", MB_OK | MB_ICONERROR);
			exit(1);
		}

		_beginthread(HackCheck, 0, 0);
		//_beginthread(FCheck, 0, 0);
		_beginthread(ChecksumCheck, 0, 0);
		DisableProtection();
		DetourTransactionBegin();
		//DetourAttach(&(PVOID&)Engine::KSocket::PacketParser, PacketParser);
		//DetourAttach(&(PVOID&)Engine::KSocket::PacketParser2, PacketParser2);
		//DetourAttach(&(PVOID&)Engine::KSocket::PacketParser3, PacketParser3);
		if (GetWinBuild() >= 2004) {
			DetourAttach(&(PVOID&)Engine::XTrap::Load, XTrapDisable);
			DetourAttach(&(PVOID&)Engine::XTrap::LoadOther, XTrapDisable);
		}

		DetourAttach(&(PVOID&)Engine::KGameSys::CreateItem, CreateItem);
		DetourAttach(&(PVOID&)Engine::KGameSys::CreateRidingInventorySlots, CreateRidingInventorySlots);
		//DetourAttach(&(PVOID&)Engine::KGameSys::MapReadFile, ZIPProtection);
		//DetourAttach(&(PVOID&)Engine::KGameSys::AddChattingMessage, ChatBoxText);
		DetourAttach(&(PVOID&)Engine::KGameSys::DismissWindow, WindowClose);
		DetourAttach(&(PVOID&)Engine::KGameSys::PressKeyState, PressKeyState);
		DetourAttach(&(PVOID&)Engine::KGameSys::LoadLogo, LoadLogo);
		DetourAttach(&(PVOID&)Engine::KGameSys::CanAttack, CanAttack);
		DetourAttach(&(PVOID&)Engine::KSocket::SendPacket, SendPacket);
		DetourAttach(&(PVOID&)Engine::KSocket::Destroy, Destroy);
		DetourAttach(&(PVOID&)Engine::KSocket::Disconnect, Disconnect);
		DetourAttach(&(PVOID&)Engine::KGameSys::UpdateHonorType, UpdateHonorName);
		DetourAttach(&(PVOID&)Engine::KGameSys::HonorName, HonorName);
		DetourAttach(&(PVOID&)KalItemShop1, MyKalItemShop1);
		DetourAttach(&(PVOID&)KalItemShop2, MyKalItemShop2);
		DetourAttach(&(PVOID&)KalItemShop3, MyKalItemShop3);
		DetourAttach(&(PVOID&)Engine::KGameSys::OnOk, OnOk);
		DetourAttach(&(PVOID&)Engine::KGameSys::OnCancel, OnCancel);
		DetourAttach(&(PVOID&)IsPlayerCheck, MyIsPlayerCheck);
		DetourAttach(&(PVOID&)PlayerTick, MyPlayerTick);
		DetourAttach(&(PVOID&)IsPlayerAttack, MyIsPlayerAttack);
		DetourAttach(&(PVOID&)SetTip, MySetTip);
		DetourAttach(&(PVOID&)Engine::KGameSys::PressKey, PressKey);
		DetourAttach(&(PVOID&)Engine::KGameSys::CheckForDss, CheckForDss);
		DetourAttach(&(PVOID&)Engine::KGameSys::MakeTip, MakeTip);
		DetourAttach(&(PVOID&)SkillButton, MySkillButton);
		DetourAttach(&(PVOID&)Engine::CEditor::PWDPK, PKHide);
		DetourAttach(&(PVOID&)Engine::KGameSys::OnUseItem, OnUseItem);
		DetourTransactionCommit();
		break;
	}
	case DLL_PROCESS_DETACH:
	{
		// Signal thread to terminate and wait briefly
		g_bHackCheckRunning = false;
		Sleep(500); // Give thread time to exit gracefully

		// Shutdown memory protection system
		MemoryProtection::Shutdown();

		FinalizeADVAPI32();
		BofFix();
		SwitchTable();
		DisableProtection();
		DetourTransactionBegin();

		if (GetWinBuild() >= 1004) {
			DetourDetach(&(PVOID&)Engine::XTrap::Load, XTrapDisable);
			DetourDetach(&(PVOID&)Engine::XTrap::LoadOther, XTrapDisable);
		}

		DetourDetach(&(PVOID&)Engine::KGameSys::CreateItem, CreateItem);
		DetourDetach(&(PVOID&)Engine::KGameSys::CreateRidingInventorySlots, CreateRidingInventorySlots);
		//DetourDetach(&(PVOID&)Engine::KGameSys::MapReadFile, ZIPProtection);
		//DetourDetach(&(PVOID&)Engine::KGameSys::AddChattingMessage, ChatBoxText);
		DetourDetach(&(PVOID&)Engine::KGameSys::DismissWindow, WindowClose);
		DetourDetach(&(PVOID&)Engine::KGameSys::PressKeyState, PressKeyState);
		DetourDetach(&(PVOID&)Engine::KGameSys::LoadLogo, LoadLogo);
		DetourDetach(&(PVOID&)Engine::KSocket::SendPacket, SendPacket);
		DetourDetach(&(PVOID&)Engine::KSocket::Destroy, Destroy);
		DetourDetach(&(PVOID&)Engine::KSocket::Disconnect, Disconnect);
		DetourDetach(&(PVOID&)Engine::KGameSys::CanAttack, CanAttack);
		DetourDetach(&(PVOID&)Engine::KGameSys::UpdateHonorType, UpdateHonorName);
		DetourDetach(&(PVOID&)Engine::KGameSys::HonorName, HonorName);
		DetourDetach(&(PVOID&)KalItemShop1, MyKalItemShop1);
		DetourDetach(&(PVOID&)KalItemShop2, MyKalItemShop2);
		DetourDetach(&(PVOID&)KalItemShop3, MyKalItemShop3);
		DetourDetach(&(PVOID&)Engine::KGameSys::OnOk, OnOk);
		DetourDetach(&(PVOID&)Engine::KGameSys::OnCancel, OnCancel);
		DetourDetach(&(PVOID&)IsPlayerCheck, MyIsPlayerCheck);
		DetourDetach(&(PVOID&)PlayerTick, MyPlayerTick);
		DetourDetach(&(PVOID&)IsPlayerAttack, MyIsPlayerAttack);
		DetourDetach(&(PVOID&)SetTip, MySetTip);
		DetourDetach(&(PVOID&)Engine::KGameSys::PressKey, PressKey);
		DetourDetach(&(PVOID&)Engine::KGameSys::CheckForDss, CheckForDss);
		DetourDetach(&(PVOID&)Engine::KGameSys::MakeTip, MakeTip);
		DetourDetach(&(PVOID&)SkillButton, MySkillButton);
		DetourDetach(&(PVOID&)Engine::CEditor::PWDPK, PKHide);
		DetourDetach(&(PVOID&)Engine::KGameSys::OnUseItem, OnUseItem);
		DetourTransactionCommit();
		break;
	}
	}
	return TRUE;
}